import 'dart:async';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'dart:convert';

import '../../Auth/Auth.dart';
import '../../core/error/exceptions.dart';
import '../../core/network/network_info.dart';
import '../../core/utils/logger.dart';
import '../../core/utils/retry_helper.dart';

abstract class ApiService {
  /// Make a POST request to the API with improved error handling
  static Future<http.Response> postRequest(
      String apiUrl, String functionName, Map<String, dynamic> body, ) async {
    // Check for network connectivity first
    final isConnected = await NetworkInfoImpl.instance.isConnected;
    if (!isConnected) {
      throw NetworkException('No internet connection available');
    }

    // Get authentication token
    String? token = await Auth.getToken();
    if (token == null) {
      throw AuthException('Authentication token is missing');
    }

    final requestHeaders = {
      'Content-Type': 'application/json',
      'spring.cloud.function.definition': functionName,
      'auth': token
    };

    return RetryHelper.retry(
      operation: () async {
        try {
          final response = await http
              .post(
                Uri.parse(apiUrl),
                headers: requestHeaders,
                body: jsonEncode(body),
              )
              .timeout(const Duration(seconds: 30));

          if (response.statusCode == 200) {
            return response;
          } else if (response.statusCode == 401 || response.statusCode == 403) {
            throw AuthException('Authentication failed',
                code: response.statusCode.toString());
          } else if (response.statusCode >= 500) {
            throw ServerException('Server error',
                code: response.statusCode.toString());
          } else {
            throw ApiException('API error: ${response.statusCode}',
                statusCode: response.statusCode);
          }
        } on SocketException catch (e) {
          throw NetworkException('Connection error', details: e.toString());
        } on TimeoutException catch (e) {
          throw NetworkException('Request timed out', details: e.toString());
        } on FormatException catch (e) {
          throw JsonParseException('Invalid data format', details: e.toString());
        } catch (e) {
          if (e is AppException) {
            rethrow;
          }
          Logger.error('Unexpected error in API request', error: e);
          throw ServerException('Unexpected error occurred');
        }
      },
      retryIf: (e) => RetryHelper.shouldRetryException(e),
      onRetry: (e, attempt) {
        Logger.info('Retrying API request (attempt $attempt)', error: e);
      },
    );
  }
}