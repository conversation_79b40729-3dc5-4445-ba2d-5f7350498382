import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:http/http.dart' as http;
import 'package:phonepe_payment_sdk/phonepe_payment_sdk.dart';
import '../../backend/rest/user_details.dart';
import '../../core/config/app_config.dart';
import '../../core/utils/logger.dart';

class PaymentService {
  late final String merchantId;
  late String transactionId;
  final AppConfig _config = AppConfig();
  final Dio _dio = Dio();

  PaymentService() {
    merchantId = _config.getApiKey('phonepe_merchant_id') ?? '';
  }

  Future<void> startTransaction(double amount, Function onSuccess, Function onError) async {
    try {
      int convertedAmount = (amount * 100).toInt();

      final authToken = await fetchAuthToken();
      if (authToken == null) throw Exception("Failed to obtain auth token");

      transactionId = await UserDetails.getMerchantTransactionId();
      final orderData = await createOrder(authToken, convertedAmount);
      if (orderData == null) throw Exception("Failed to create order");

      bool isInitialized = await _initializeSdk();
      if (!isInitialized) throw Exception("Could Not Initialize");

      final String requestData = await _generateRequestData(authToken, orderData);
      await PhonePePaymentSdk.startTransaction(requestData, "top3.UI_V1").then((response) {
        _handleTransactionResponse(authToken, response!['status'], onSuccess, onError);
      });
    } catch (error) {
      onError("Error during transaction: $error");
    }
  }

  Future<String?> fetchAuthToken() async {
    try {
      final oauthEndpoint = _config.getEndpoint('phonepe_oauth')!;
      final clientId = _config.getApiKey('phonepe_client_id');
      final clientSecret = _config.getSecret('phonepe_client_secret');

      final response = await _dio.post(
        oauthEndpoint,
        data: {
          'client_id': clientId,
          'client_version': '1',
          'client_secret': clientSecret,
          'grant_type': 'client_credentials',
        },
        options: Options(
          contentType: Headers.formUrlEncodedContentType,
          headers: {'Accept': 'application/json'},
        ),
      );

      if (response.statusCode == 200) {
        return response.data['access_token'];
      } else {
        Logger.error("Auth Token Error", error: response.data);
        return null;
      }
    } on DioException catch (e) {
      Logger.error("PhonePe API Error", error: e.response?.data ?? e.message);
      return null;
    }
  }

  Future<Map<String, dynamic>?> createOrder(String accessToken, int amount) async {
    try {
      final orderEndpoint = _config.getEndpoint('phonepe_order')!;
      final requestBody = {
        "merchantOrderId": transactionId,
        "amount": amount,
        "paymentFlow": {"type": "PG_CHECKOUT"}
      };

      final response = await http.post(
        Uri.parse(orderEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'O-Bearer $accessToken',
        },
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        Logger.error("Create Order Error", error: response.body);
        return null;
      }
    } catch (e) {
      Logger.error("Create Order Exception", error: e);
      return null;
    }
  }

  Future<bool> _initializeSdk() async {
    return await PhonePePaymentSdk.init('SANDBOX', merchantId, transactionId, true);
  }

  Future<String> _generateRequestData(String authToken, Map<String, dynamic> orderData) async {
    Map<String, dynamic> payload = {
      "orderId": orderData["orderId"],
      "merchantId": merchantId,
      "token": orderData["token"],
      "paymentMode": {"type": "PAY_PAGE"},
    };
    return jsonEncode(payload);
  }

  Future<void> _handleTransactionResponse(String authToken, String status, Function onSuccess, Function onError) async {
    try {
      await updateStatus(authToken);
      if (status == "SUCCESS") {
        onSuccess();
      } else if (status == "FAILURE") {
        onError("Transaction Failed!");
      }
    } catch (e) {
      onError("Status check error: $e");
    }
  }

  Future<void> updateStatus(String authToken) async {
    try {
      final statusUrl = '${_config.getEndpoint('phonepe_status')!}/$transactionId/status';

      final response = await http.get(
        Uri.parse(statusUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'O-Bearer $authToken',
        },
      );

      if (response.statusCode == 200) {
        await UserDetails.saveTransaction(response.body);
        Logger.info("Transaction status updated successfully");
      } else {
        Logger.error("Failed to retrieve status", error: "Status Code: ${response.statusCode}, Body: ${response.body}");
        throw HttpException("Failed to retrieve status, Status Code: ${response.statusCode}");
      }
    } catch (e) {
      Logger.error("Status retrieval failed", error: e);
      throw HttpException("Status retrieval failed: $e");
    }
  }
}